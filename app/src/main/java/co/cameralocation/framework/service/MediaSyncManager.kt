package co.cameralocation.framework.service

import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import co.cameralocation.framework.presentation.model.media.CameraGPSSavedMediaItem
import co.cameralocation.framework.repository.MediaRepository
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class MediaSyncManager @Inject constructor(
    private val context: Context,
    private val mediaRepository: MediaRepository
) {
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private val handler = Handler(Looper.getMainLooper())
    
    private val _mediaDeletionEvents = MutableSharedFlow<MediaDeletionEvent>()
    val mediaDeletionEvents: SharedFlow<MediaDeletionEvent> = _mediaDeletionEvents.asSharedFlow()
    
    private val _mediaSyncEvents = MutableSharedFlow<MediaSyncEvent>()
    val mediaSyncEvents: SharedFlow<MediaSyncEvent> = _mediaSyncEvents.asSharedFlow()
    
    private var contentObserver: ContentObserver? = null
    private var isObserving = false
    
    // Cache of current media items for comparison
    private var cachedMediaItems: List<CameraGPSSavedMediaItem> = emptyList()
    
    fun startObserving() {
        if (isObserving) return
        
        Timber.d("Starting media observation")
        
        // Load initial cache
        scope.launch {
            try {
                cachedMediaItems = mediaRepository.getAllMedia()
                Timber.d("Loaded ${cachedMediaItems.size} media items to cache")
            } catch (e: Exception) {
                Timber.e(e, "Error loading initial media cache")
            }
        }
        
        contentObserver = object : ContentObserver(handler) {
            override fun onChange(selfChange: Boolean, uri: Uri?) {
                super.onChange(selfChange, uri)
                Timber.d("Media content changed: $uri")
                // Delay to allow MediaStore to update completely
                scope.launch {
                    delay(500) // Wait for MediaStore to stabilize
                    handleMediaChange()
                }
            }
        }
        
        // Register observer for external storage media changes
        context.contentResolver.registerContentObserver(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            true,
            contentObserver!!
        )
        
        context.contentResolver.registerContentObserver(
            MediaStore.Video.Media.EXTERNAL_CONTENT_URI,
            true,
            contentObserver!!
        )
        
        isObserving = true
        Timber.d("Media observation started")
    }
    
    fun stopObserving() {
        if (!isObserving) return
        
        Timber.d("Stopping media observation")
        
        contentObserver?.let { observer ->
            context.contentResolver.unregisterContentObserver(observer)
        }
        contentObserver = null
        isObserving = false
        
        Timber.d("Media observation stopped")
    }
    
    private fun handleMediaChange() {
        scope.launch {
            try {
                // Get current media items from repository
                val currentMediaItems = mediaRepository.getAllMedia()

                Timber.d("handleMediaChange: currentMediaItems: $currentMediaItems")

                // Find actually deleted items by verifying file existence
                val deletedItems = findActuallyDeletedItems(cachedMediaItems)

                Timber.d("handleMediaChange: deletedItems: $deletedItems")

                if (deletedItems.isNotEmpty()) {
                    Timber.d("Found ${deletedItems.size} actually deleted media items")

                    // Delete MediaItems from database for deleted files
                    deletedItems.forEach { deletedItem ->
                        try {
                            // Delete from database only (file is already deleted from device)
                            val deleteSuccess = mediaRepository.deleteMedia(deletedItem)
                            if (deleteSuccess) {
                                Timber.d("Successfully deleted MediaItem from database: ${deletedItem.id}")
                            } else {
                                Timber.w("Failed to delete MediaItem from database: ${deletedItem.id}")
                            }
                        } catch (e: Exception) {
                            Timber.e(e, "Error deleting MediaItem from database: ${deletedItem.id}")
                        }

                        // Emit deletion events
                        _mediaDeletionEvents.emit(
                            MediaDeletionEvent.MediaDeleted(deletedItem)
                        )
                    }

                    // Emit sync event
                    _mediaSyncEvents.emit(MediaSyncEvent.MediaSyncRequired)
                }

                // Update cache
                cachedMediaItems = currentMediaItems

            } catch (e: Exception) {
                Timber.e(e, "Error handling media change")
                _mediaSyncEvents.emit(MediaSyncEvent.SyncError(e.message ?: "Unknown error"))
            }
        }
    }
    
    private fun findActuallyDeletedItems(
        cached: List<CameraGPSSavedMediaItem>
    ): List<CameraGPSSavedMediaItem> {
        Timber.d("findActuallyDeletedItems: cached: $cached")
        return cached.filter { cachedItem ->
            // Check if the actual file still exists
            val fileExists = try {
                val file = File(cachedItem.path)
                Timber.d("findActuallyDeletedItems: path: ${cachedItem.path}, fileExists: ${file.exists()}")
                file.exists()
            } catch (e: Exception) {
                // If we can't check the file, assume it's deleted
                Timber.w(e, "Cannot check file existence for: ${cachedItem.path}")
                false
            }

            // Also check if the URI is still accessible via ContentResolver
            val uriAccessible = try {
                context.contentResolver.openInputStream(cachedItem.uri)?.use {
                    true
                } ?: false
            } catch (e: Exception) {
                // If we can't access the URI, the file is likely deleted
                false
            }

            Timber.d("findActuallyDeletedItems: fileExists: $fileExists, uriAccessible: $uriAccessible")

            // Item is considered deleted if both file and URI are not accessible
            val isDeleted = !fileExists && !uriAccessible

            Timber.d("findActuallyDeletedItems: isDeleted: $isDeleted")

            if (isDeleted) {
                Timber.d("File actually deleted: ${cachedItem.path}")
            }

            isDeleted
        }
    }
    
    fun syncMediaItems() {
        scope.launch {
            try {
                _mediaSyncEvents.emit(MediaSyncEvent.SyncStarted)
                
                // Reload media items
                val updatedMediaItems = mediaRepository.getAllMedia()
                cachedMediaItems = updatedMediaItems
                
                _mediaSyncEvents.emit(MediaSyncEvent.SyncCompleted(updatedMediaItems))
                
            } catch (e: Exception) {
                Timber.e(e, "Error syncing media items")
                _mediaSyncEvents.emit(MediaSyncEvent.SyncError(e.message ?: "Sync failed"))
            }
        }
    }
    
    fun isMediaItemDeleted(mediaItem: CameraGPSSavedMediaItem): Boolean {
        return !cachedMediaItems.any { it.uri == mediaItem.uri }
    }
    
    companion object {
        const val TAG = "MediaSyncManager"
    }
}

sealed class MediaDeletionEvent {
    data class MediaDeleted(val mediaItem: CameraGPSSavedMediaItem) : MediaDeletionEvent()
}

sealed class MediaSyncEvent {
    object MediaSyncRequired : MediaSyncEvent()
    object SyncStarted : MediaSyncEvent()
    data class SyncCompleted(val mediaItems: List<CameraGPSSavedMediaItem>) : MediaSyncEvent()
    data class SyncError(val message: String) : MediaSyncEvent()
}
