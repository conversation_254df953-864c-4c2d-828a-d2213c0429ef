package co.cameralocation.framework.presentation.compass

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import co.cameralocation.framework.presentation.compass.detail_compass.DetailCompassFragment
import co.cameralocation.framework.presentation.compass.digital_compass.DigitalCompassFragment
import co.cameralocation.framework.presentation.compass.map.CompassMapFragment

class CompassPagerAdapter(fm: Fragment) : FragmentStateAdapter(fm) {
    override fun getItemCount(): Int {
        return 3
    }

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> DigitalCompassFragment()
            1 -> DetailCompassFragment()
            2 -> CompassMapFragment()
            else -> DigitalCompassFragment()
        }
    }

}