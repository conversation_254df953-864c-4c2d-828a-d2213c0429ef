package co.cameralocation.framework.presentation.compass.digital_compass

import android.annotation.SuppressLint
import android.hardware.SensorEvent
import android.view.View
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import androidx.core.content.ContextCompat
import kotlin.math.roundToInt
import kotlin.math.sqrt
import co.cameralocation.R

@SuppressLint("SetTextI18n")
fun DigitalCompassFragment.observeData() {

    commonViewModel.currentDegreeOrientation.observe(viewLifecycleOwner) {
        val toTalDegreeOrientation = it
        if (isFirstGetValue) {
            firstAngle = toTalDegreeOrientation
            isFirstGetValue = false
        }

        binding.tvDegree.text = "${toTalDegreeOrientation}*${findDirection(toTalDegreeOrientation)}"
        val ra = RotateAnimation(
            currentDegree,
            -toTalDegreeOrientation.toFloat(),
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        )
        ra.duration = 210
        ra.fillAfter = true
        binding.imgCompass.startAnimation(ra)
        currentDegree = -toTalDegreeOrientation.toFloat()


    }

    commonViewModel.newAngleDes.observe(viewLifecycleOwner) { newAngelDes ->
        binding.imgNewDes.visibility = View.VISIBLE
        commonViewModel.currentDegreeOrientation.observe(viewLifecycleOwner) {
            val raNewDes = RotateAnimation(
                currentDegreeNewDes,
                -(it.toFloat() - newAngelDes.toFloat()),
                Animation.RELATIVE_TO_SELF,
                0.5f,
                Animation.RELATIVE_TO_SELF,
                0.5f
            )
            raNewDes.duration = 210
            raNewDes.fillAfter = true
            binding.layoutShowDes.startAnimation(raNewDes)
            currentDegreeNewDes = -(it.toFloat() - newAngelDes.toFloat())
        }
    }
}

fun findDirection(degree: Int): String {
    if ((degree in 0..44) || degree == 360) {
        return "N"
    }
    if (degree in 45..89) {
        return "NE"
    }
    if (degree in 90..134) {
        return "E"
    }
    if (degree in 135..179) {
        return "SE"
    }
    if (degree in 180..224) {
        return "S"
    }
    if (degree in 225..269) {
        return "SW"
    }
    if (degree in 270..314) {
        return "W"
    }
    if (degree in 315..359) {
        return "NW"
    }
    return "Error Direction"
}

fun DigitalCompassFragment.processSensorMagneticChange(event: SensorEvent?) {
    val degreeX = event!!.values[0].roundToInt()
    val degreeY = event.values[1].roundToInt()
    val degreeZ = event.values[2].roundToInt()

    val toTalIntensity =
        sqrt((degreeX * degreeX + degreeY * degreeY + degreeZ * degreeZ).toDouble())
    binding.tvMagnet.text = toTalIntensity.toInt().toString()
}

fun DigitalCompassFragment.onClickListener() {
    binding.vTapToNavigate.setOnClickListener {
        if (isNavigate) {
            binding.viewArc.visibility = View.GONE
            it.setBackgroundResource(R.drawable.button_border_circle_navigate)
            binding.tvTap.apply {
                text = context.getString(R.string.tap_here)
                setTextColor(ContextCompat.getColor(context, R.color.grey_898b90))
            }
            isNavigate = false
        } else {
            isFirstGetValue = true
            binding.viewArc.visibility = View.VISIBLE
            it.setBackgroundResource(R.drawable.button_border_circle_navigate)
            binding.tvTap.apply {
                text = context.getString(R.string.tap_again_to_stop)
                setTextColor(ContextCompat.getColor(context, R.color.blue_28abf5))
            }

            commonViewModel.currentDegreeOrientation.observe(viewLifecycleOwner) { currentAngle ->

                var sweepAngle = firstAngle - currentAngle
                val startAngle = 270
                var oppFirstAngle = 0

                if (firstAngle < 180) {
                    oppFirstAngle = firstAngle + 180
                    if (currentAngle <= oppFirstAngle) {
                        binding.viewArc.updateArc(
                            startAngle.toFloat(),
                            sweepAngle.toFloat()
                        )
                    } else {
                        sweepAngle = 180 - (currentAngle - oppFirstAngle)
                        binding.viewArc.updateArc(
                            startAngle.toFloat(),
                            sweepAngle.toFloat()
                        )
                    }
                } else {
                    oppFirstAngle = firstAngle - 180
                    if (currentAngle > firstAngle || currentAngle < oppFirstAngle) {
                        if (currentAngle < 180) {
                            sweepAngle = -(180 - oppFirstAngle) - currentAngle
                            binding.viewArc.updateArc(
                                startAngle.toFloat(),
                                sweepAngle.toFloat()
                            )
                        } else {
                            binding.viewArc.updateArc(
                                startAngle.toFloat(),
                                sweepAngle.toFloat()
                            )
                        }
                    } else {
                        sweepAngle = firstAngle - currentAngle
                        binding.viewArc.updateArc(
                            startAngle.toFloat(),
                            sweepAngle.toFloat()
                        )
                    }
                }
            }
            isNavigate = true
        }
    }
}