package co.cameralocation.framework.presentation.model.editmediavideo

import co.cameralocation.framework.presentation.model.media.CameraGPSSavedMediaItem

/*
data class EditMediaVideoUiState(
    val cameraGPSSavedMediaItem: CameraGPSSavedMediaItem? = null,
    val isLoading: Boolean = false,
    val error: String? = null,
    val displayCutControls: Boolean = false,
    val isMediaDeleted: <PERSON>olean = false,
    val shouldExitDueToMediaDeletion: Boolean = false
)
*/
