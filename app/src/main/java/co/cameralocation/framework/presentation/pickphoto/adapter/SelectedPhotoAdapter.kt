package co.cameralocation.framework.presentation.pickphoto.adapter

import androidx.databinding.ViewDataBinding
import com.bumptech.glide.Glide
import co.cameralocation.R
import co.cameralocation.databinding.ItemSelectedPhotoBinding
import co.cameralocation.databinding.ItemSelectedPhotoEmptyBinding
import co.cameralocation.framework.presentation.common.BaseListAdapter
import co.cameralocation.framework.presentation.common.createDiffCallback
import co.cameralocation.framework.presentation.model.pickphoto.DevicePhoto
import co.cameralocation.util.setPreventDoubleClick

class SelectedPhotoAdapter : BaseListAdapter<SelectedDevicePhoto, ViewDataBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem == newItem },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
    )
) {
    var onRemoveClick: ((DevicePhoto) -> Unit)? = null

    override fun getItemViewType(position: Int): Int {
        return when(getItem(position)) {
            is SelectedDevicePhoto.Photo -> VIEW_TYPE_PHOTO
            is SelectedDevicePhoto.Empty -> VIEW_TYPE_EMPTY
        }
    }

    override fun getLayoutRes(viewType: Int): Int {
        return when (viewType) {
            VIEW_TYPE_PHOTO -> R.layout.item_selected_photo
            VIEW_TYPE_EMPTY -> R.layout.item_selected_photo_empty
            else -> throw IllegalArgumentException("Invalid view type: $viewType")
        }
    }

    override fun bindView(
        binding: ViewDataBinding,
        item: SelectedDevicePhoto,
        position: Int
    ) {
        val item = getItem(position)

        when (item) {
            is SelectedDevicePhoto.Photo -> {
                val binding = binding as ItemSelectedPhotoBinding
                binding.apply {
                    // Load photo
                    Glide.with(binding.root.context)
                        .load(item.devicePhoto.uri)
                        .centerCrop()
                        .into(ivPhoto)

                    // Set remove button click listener
                    btnRemove.setPreventDoubleClick {
                        onRemoveClick?.invoke(item.devicePhoto)
                    }
                }
            }

            is SelectedDevicePhoto.Empty -> {
                // Do nothing
            }
        }
    }

    override fun bindView(
        binding: ViewDataBinding,
        item: SelectedDevicePhoto,
        position: Int,
        payloads: MutableList<Any>
    ) {
        bindView(binding, item, position)
    }

    companion object {
        const val VIEW_TYPE_PHOTO = 0
        const val VIEW_TYPE_EMPTY = 1
    }
}

sealed class SelectedDevicePhoto {
    data class Photo(val devicePhoto: DevicePhoto) : SelectedDevicePhoto()
    data object Empty : SelectedDevicePhoto()
}